import React from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import { 
  ShieldCheckIcon, 
  DocumentTextIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
  ClockIcon,
  PhoneIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';

const InsurancePage: React.FC = () => {
  const coverageTypes = [
    {
      title: "Basic Coverage",
      description: "Standard protection included with all moves",
      coverage: "₹60 per kg",
      features: [
        "Basic liability protection",
        "Coverage for common damages",
        "Standard claim processing",
        "No additional cost"
      ],
      icon: ShieldCheckIcon,
      popular: false,
    },
    {
      title: "Full Value Protection",
      description: "Comprehensive coverage for your belongings",
      coverage: "Full replacement value",
      features: [
        "Complete replacement value",
        "Coverage for all types of damage",
        "Priority claim processing",
        "Repair or replacement options",
        "High-value item protection"
      ],
      icon: CheckCircleIcon,
      popular: true,
    },
    {
      title: "Premium Coverage",
      description: "Maximum protection for valuable items",
      coverage: "Up to ₹50 lakhs",
      features: [
        "Highest coverage limits",
        "Specialized handling coverage",
        "Expedited claim resolution",
        "Dedicated claims specialist",
        "Coverage for rare/unique items"
      ],
      icon: CurrencyDollarIcon,
      popular: false,
    },
  ];

  const claimProcess = [
    {
      step: "1",
      title: "Report Damage",
      description: "Contact us immediately upon discovering damage or loss",
      icon: ExclamationTriangleIcon,
    },
    {
      step: "2",
      title: "Document Evidence",
      description: "Take photos and gather documentation of the damage",
      icon: DocumentTextIcon,
    },
    {
      step: "3",
      title: "File Claim",
      description: "Submit your claim with all required documentation",
      icon: DocumentTextIcon,
    },
    {
      step: "4",
      title: "Assessment",
      description: "Our team will assess the claim and determine coverage",
      icon: CheckCircleIcon,
    },
    {
      step: "5",
      title: "Resolution",
      description: "Receive compensation or replacement for covered items",
      icon: CurrencyDollarIcon,
    },
  ];

  const coveredItems = [
    "Household furniture and appliances",
    "Electronics and entertainment systems",
    "Clothing and personal belongings",
    "Books and documents",
    "Artwork and collectibles",
    "Kitchen items and cookware",
    "Sports and recreational equipment",
    "Office equipment and supplies"
  ];

  const excludedItems = [
    "Cash, jewelry, and precious metals",
    "Important documents (passports, certificates)",
    "Perishable food items",
    "Hazardous materials",
    "Live plants and animals",
    "Firearms and ammunition",
    "Items packed by customer",
    "Pre-existing damage"
  ];

  return (
    <Layout
      title="Insurance Coverage - VayuVector"
      description="Comprehensive insurance coverage options for your international move. Protect your belongings with VayuVector's flexible insurance plans."
    >
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-green-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <ShieldCheckIcon className="h-12 w-12 text-green-600" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                Insurance Coverage
              </h1>
            </div>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Protect your belongings during international relocation with our 
              comprehensive insurance coverage options. Peace of mind for your move.
            </p>
            <Link 
              href="/quote" 
              className="btn bg-green-600 text-white hover:bg-green-700 font-semibold text-lg px-8 py-3"
            >
              Get Insurance Quote
            </Link>
          </div>
        </div>
      </section>

      {/* Coverage Options */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Coverage Options
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the insurance coverage that best fits your needs and budget. 
              All options provide reliable protection for your belongings.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {coverageTypes.map((coverage, index) => {
              const IconComponent = coverage.icon;
              return (
                <div key={index} className={`card p-8 relative ${coverage.popular ? 'ring-2 ring-green-600' : ''}`}>
                  {coverage.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-green-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                        Most Popular
                      </span>
                    </div>
                  )}
                  
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{coverage.title}</h3>
                    <p className="text-green-600 font-semibold mb-2">{coverage.coverage}</p>
                    <p className="text-gray-600 text-sm">{coverage.description}</p>
                  </div>

                  <div className="space-y-3 mb-8">
                    {coverage.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircleIcon className="h-4 w-4 text-green-600 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link 
                    href="/quote" 
                    className={`btn w-full text-center ${
                      coverage.popular 
                        ? 'bg-green-600 text-white hover:bg-green-700' 
                        : 'btn-outline border-green-600 text-green-600 hover:bg-green-600 hover:text-white'
                    }`}
                  >
                    Select Plan
                  </Link>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Claims Process */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Claims Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our streamlined claims process ensures quick and fair resolution 
              of any damage or loss during your move.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {claimProcess.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <div key={index} className="text-center">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-secondary-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{step.step}</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{step.title}</h3>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Coverage Details */}
      <section className="section">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Covered Items */}
            <div className="card p-8">
              <div className="flex items-center space-x-3 mb-6">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <h3 className="text-2xl font-bold text-gray-900">What's Covered</h3>
              </div>
              <ul className="space-y-3">
                {coveredItems.map((item, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full flex-shrink-0"></div>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Excluded Items */}
            <div className="card p-8">
              <div className="flex items-center space-x-3 mb-6">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
                <h3 className="text-2xl font-bold text-gray-900">What's Not Covered</h3>
              </div>
              <ul className="space-y-3">
                {excludedItems.map((item, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full flex-shrink-0"></div>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Important Information */}
      <section className="section bg-yellow-50">
        <div className="container-custom">
          <div className="card p-8 border-yellow-200">
            <div className="flex items-start space-x-4">
              <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600 flex-shrink-0 mt-1" />
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Important Information</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• Claims must be reported within 24 hours of delivery</li>
                  <li>• All damage must be noted on the delivery receipt</li>
                  <li>• Original receipts required for high-value items</li>
                  <li>• Coverage limits may apply to certain item categories</li>
                  <li>• Deductibles may apply depending on coverage type</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="section bg-primary-900 text-white">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Need Help with Insurance?
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Our insurance specialists are here to help you choose the right coverage 
              and assist with any claims.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <EnvelopeIcon className="h-12 w-12 text-accent-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Email Support</h3>
              <a 
                href="mailto:<EMAIL>"
                className="text-accent-400 hover:text-accent-300 transition-colors"
              >
                <EMAIL>
              </a>
            </div>
            <div className="text-center">
              <ClockIcon className="h-12 w-12 text-accent-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Response Time</h3>
              <p className="text-gray-300">Claims processed within 48 hours</p>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link 
              href="/quote" 
              className="btn bg-accent-600 text-white hover:bg-accent-700 text-lg px-8 py-3 font-semibold"
            >
              Get Insurance Quote
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default InsurancePage;
