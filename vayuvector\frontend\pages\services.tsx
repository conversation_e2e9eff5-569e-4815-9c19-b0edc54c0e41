import React from 'react';
import Layout from '@/components/Layout';
import { 
  HomeIcon, 
  TruckIcon, 
  DocumentTextIcon,
  ArchiveBoxIcon,
  ShieldCheckIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const ServicesPage: React.FC = () => {
  const mainServices = [
    {
      id: 'residential',
      title: 'Residential Relocation',
      description: 'Complete household moving services with professional care for all your belongings.',
      icon: HomeIcon,
      features: [
        'Professional packing & unpacking',
        'Furniture disassembly & assembly',
        'Appliance disconnection & reconnection',
        'Fragile item special handling',
        'Room-by-room organization',
        'Inventory management',
        'Custom crating for valuables',
        'Climate-controlled storage'
      ],

      color: 'primary',
    },
    {
      id: 'vehicle',
      title: 'Vehicle Transportation',
      description: 'Safe and secure transportation of your vehicles with comprehensive insurance coverage.',
      icon: TruckIcon,
      features: [
        'Door-to-door car shipping',
        'Motorcycle transportation',
        'Classic & luxury vehicle handling',
        'International documentation',
        'Real-time GPS tracking',
        'Enclosed transport options',
        'Multi-vehicle discounts',
        'Customs clearance assistance'
      ],

      color: 'secondary',
    },
    {
      id: 'documentation',
      title: 'Documentation Support',
      description: 'Complete assistance with all paperwork and legal requirements for your international move.',
      icon: DocumentTextIcon,
      features: [
        'Visa & work permit guidance',
        'Customs clearance assistance',
        'School enrollment support',
        'Banking setup coordination',
        'Local registration help',
        'Tax documentation',
        'Insurance transfers',
        'Legal document translation'
      ],

      color: 'accent',
    },
  ];

  const additionalServices = [
    {
      title: 'Storage Solutions',
      description: 'Secure storage facilities with flexible terms',
      icon: ArchiveBoxIcon,
      features: ['Climate-controlled', '24/7 security', 'Easy access'],
    },
    {
      title: 'Insurance Coverage',
      description: 'Comprehensive protection for your belongings',
      icon: ShieldCheckIcon,
      features: ['Full replacement value', 'Transit coverage', 'Damage claims'],
    },
    {
      title: '24/7 Support',
      description: 'Round-the-clock customer assistance',
      icon: ClockIcon,
      features: ['Live chat', 'Phone support', 'Emergency assistance'],
    },
  ];

  return (
    <Layout
      title="Our Services - VayuVector"
      description="Comprehensive international relocation services including residential moving, vehicle transportation, and documentation support. Professional door-to-door service worldwide."
    >
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Comprehensive Relocation Services
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              From residential moves to vehicle transportation, we provide end-to-end 
              solutions for your international relocation needs. Our expert team handles 
              every detail so you can focus on your new beginning.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary text-lg px-8 py-3">
                Get Free Quote
              </button>
              <button className="btn-outline text-lg px-8 py-3">
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Services */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Core Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We specialize in three main areas of international relocation, 
              each designed to make your move as seamless as possible.
            </p>
          </div>

          <div className="space-y-16">
            {mainServices.map((service, index) => {
              const IconComponent = service.icon;
              const isEven = index % 2 === 0;
              
              return (
                <div key={service.id} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}>
                  {/* Content */}
                  <div className={isEven ? '' : 'lg:col-start-2'}>
                    <div className="flex items-center space-x-4 mb-6">
                      <div className={`w-16 h-16 rounded-xl bg-${service.color}-100 flex items-center justify-center`}>
                        <IconComponent className={`h-8 w-8 text-${service.color}-600`} />
                      </div>
                      <div>
                        <h3 className="text-2xl md:text-3xl font-bold text-gray-900">
                          {service.title}
                        </h3>
                      </div>
                    </div>
                    
                    <p className="text-lg text-gray-600 mb-6">
                      {service.description}
                    </p>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-8">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <CheckCircleIcon className={`h-5 w-5 text-${service.color}-600 flex-shrink-0`} />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-3">
                      <button className={`btn-${service.color} flex items-center justify-center space-x-2`}>
                        <span>Learn More</span>
                        <ArrowRightIcon className="h-4 w-4" />
                      </button>
                      <button className="btn-outline">
                        Get Quote
                      </button>
                    </div>
                  </div>
                  
                  {/* Image/Visual */}
                  <div className={isEven ? '' : 'lg:col-start-1 lg:row-start-1'}>
                    <div className={`aspect-video bg-gradient-to-br from-${service.color}-100 to-${service.color}-200 rounded-2xl flex items-center justify-center relative overflow-hidden`}>
                      {/* Background pattern */}
                      <div className="absolute inset-0 opacity-10">
                        <div className="grid grid-cols-4 gap-2 p-4 h-full">
                          {[...Array(16)].map((_, i) => (
                            <div key={i} className={`bg-${service.color}-600 rounded opacity-30`}></div>
                          ))}
                        </div>
                      </div>
                      <div className="relative z-10 text-center">
                        <IconComponent className={`h-20 w-20 text-${service.color}-600 opacity-70 mx-auto mb-2`} />
                        <div className={`text-${service.color}-700 font-semibold text-sm`}>{service.title}</div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Additional Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Complementary services to ensure every aspect of your move is covered.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {additionalServices.map((service, index) => {
              const IconComponent = service.icon;
              
              return (
                <div key={index} className="card-hover p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="h-8 w-8 text-gray-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {service.description}
                  </p>
                  <div className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center justify-center space-x-2">
                        <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Overview */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Service Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              A streamlined approach that ensures quality and transparency at every step.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: '01', title: 'Consultation', description: 'Free assessment of your moving needs' },
              { step: '02', title: 'Planning', description: 'Detailed moving plan and timeline' },
              { step: '03', title: 'Execution', description: 'Professional packing and transportation' },
              { step: '04', title: 'Delivery', description: 'Unpacking and setup at destination' },
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {item.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-600 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Start Your Move?
          </h2>
          <p className="text-lg mb-8 text-primary-100 max-w-2xl mx-auto">
            Get a personalized quote for your international relocation. 
            Our experts are standing by to help you plan your perfect move.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-3">
              Get Free Quote
            </button>
            <button className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-3">
              Call +1-800-VAYU-VEC
            </button>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ServicesPage;
