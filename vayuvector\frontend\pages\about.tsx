import React from 'react';
import Layout from '@/components/Layout';
import { 
  GlobeAltIcon,
  UsersIcon,
  ShieldCheckIcon,
  TrophyIcon,
  HeartIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';

const AboutPage: React.FC = () => {
  const values = [
    {
      title: 'Excellence',
      description: 'We strive for perfection in every aspect of our service, from initial consultation to final delivery.',
      icon: TrophyIcon,
      color: 'primary',
    },
    {
      title: 'Trust',
      description: 'Building lasting relationships through transparency, reliability, and consistent communication.',
      icon: ShieldCheckIcon,
      color: 'secondary',
    },
    {
      title: 'Care',
      description: 'Treating your belongings and your journey with the utmost care and personal attention.',
      icon: HeartIcon,
      color: 'accent',
    },
    {
      title: 'Innovation',
      description: 'Continuously improving our processes and technology to provide better service experiences.',
      icon: LightBulbIcon,
      color: 'green',
    },
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'CEO & Founder',
      bio: 'With over 15 years in international logistics, <PERSON> founded VayuVector to revolutionize the moving experience.',
      image: '/images/team/sarah.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Head of Operations',
      bio: '<PERSON> oversees our global network and ensures every move meets our high standards of quality.',
      image: '/images/team/michael.jpg',
    },
    {
      name: '<PERSON>',
      role: 'Customer Experience Director',
      bio: 'Emma leads our customer service team and develops programs to enhance the moving experience.',
      image: '/images/team/emma.jpg',
    },
    {
      name: 'David Thompson',
      role: 'Technology Director',
      bio: 'David drives our digital innovation, creating tools that make international moving more transparent.',
      image: '/images/team/david.jpg',
    },
  ];

  const milestones = [
    { year: '2008', event: 'VayuVector founded with a vision to transform international moving' },
    { year: '2012', event: 'Expanded to 25 countries with trusted local partners' },
    { year: '2016', event: 'Launched digital tracking platform for real-time shipment visibility' },
    { year: '2019', event: 'Reached 50,000 successful international relocations' },
    { year: '2021', event: 'Introduced AI-powered quote system and virtual surveys' },
    { year: '2024', event: 'Serving 120+ countries with 99.2% customer satisfaction rate' },
  ];

  return (
    <Layout
      title="About Us - VayuVector"
      description="Learn about VayuVector's mission to transform international moving. Meet our team, discover our values, and see how we're making global relocation seamless for professionals worldwide."
    >
      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Moving Lives, Not Just Belongings
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              For over 15 years, VayuVector has been the trusted partner for international 
              professionals seeking seamless, stress-free relocations. We don't just move 
              your possessions – we help you transition to your new life with confidence.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">50K+</div>
                <div className="text-gray-600">Successful Moves</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-secondary-600 mb-2">120+</div>
                <div className="text-gray-600">Countries Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-accent-600 mb-2">99.2%</div>
                <div className="text-gray-600">Satisfaction Rate</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="section">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Story
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  VayuVector was born from a simple observation: international moving was 
                  unnecessarily complicated, stressful, and opaque. Our founder, Sarah Mitchell, 
                  experienced this firsthand during her own corporate relocations across three 
                  continents.
                </p>
                <p>
                  In 2008, she set out to create a different kind of moving company – one that 
                  would treat each relocation as a life transition, not just a logistics operation. 
                  The name "VayuVector" combines "Vayu" (Sanskrit for wind, representing movement 
                  and change) with "Vector" (direction and purpose).
                </p>
                <p>
                  Today, we're proud to be the preferred moving partner for Fortune 500 companies 
                  and thousands of individuals who trust us with their most important transitions. 
                  Our commitment remains unchanged: to make every move a positive step forward 
                  in our customers' lives.
                </p>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-primary-100 to-secondary-200 rounded-2xl flex items-center justify-center">
                <GlobeAltIcon className="h-32 w-32 text-primary-600 opacity-50" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Values
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              These core values guide every decision we make and every service we provide.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon;
              
              return (
                <div key={index} className="card p-8 text-center">
                  <div className={`w-16 h-16 bg-${value.color}-100 rounded-full flex items-center justify-center mx-auto mb-6`}>
                    <IconComponent className={`h-8 w-8 text-${value.color}-600`} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {value.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Meet Our Leadership Team
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Experienced professionals dedicated to making your international move seamless.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="card p-6 text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-primary-400 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl">
                  {member.name.split(' ').map(n => n[0]).join('')}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-primary-600 font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-gray-600 text-sm">
                  {member.bio}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Journey
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Key milestones in our mission to transform international moving.
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold">
                      {milestone.year}
                    </div>
                  </div>
                  <div className="flex-1 pt-3">
                    <p className="text-gray-700 leading-relaxed">
                      {milestone.event}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Certifications & Awards */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Certifications & Recognition
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Industry recognition and certifications that validate our commitment to excellence.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: 'ISO 9001:2015 Certified',
                description: 'Quality management system certification for consistent service delivery.',
                icon: '🏅',
              },
              {
                title: 'FIDI Accredited',
                description: 'Member of the International Federation of International Movers.',
                icon: '🌍',
              },
              {
                title: 'Best Moving Company 2023',
                description: 'Awarded by International Business Magazine for excellence in service.',
                icon: '🏆',
              },
            ].map((item, index) => (
              <div key={index} className="card p-8 text-center">
                <div className="text-4xl mb-4">{item.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {item.title}
                </h3>
                <p className="text-gray-600">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-600 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Experience the VayuVector Difference?
          </h2>
          <p className="text-lg mb-8 text-primary-100 max-w-2xl mx-auto">
            Join thousands of satisfied customers who have trusted us with their 
            international relocations. Let us help you make your next move your best move.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn bg-white text-primary-600 hover:bg-gray-100 text-lg px-8 py-3">
              Get Free Quote
            </button>
            <button className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600 text-lg px-8 py-3">
              Schedule Consultation
            </button>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default AboutPage;
