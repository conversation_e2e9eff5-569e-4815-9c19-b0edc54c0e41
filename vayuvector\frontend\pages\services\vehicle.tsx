import React from 'react';
import Layout from '@/components/Layout';
import Link from 'next/link';
import {
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  MapPinIcon,
  DocumentTextIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

const VehicleTransportationPage: React.FC = () => {
  const vehicleTypes = [
    {
      title: 'Personal Cars',
      description: 'Sedans, hatchbacks, SUVs, and luxury vehicles transported with professional care and attention to detail.',
      features: [
        'Door-to-door pickup and delivery service',
        'Enclosed transport options for premium protection',
        'Real-time GPS tracking with regular updates',
        'Comprehensive insurance coverage up to full value',
        'Detailed pre and post-transport inspection reports',
        'Professional drivers with international experience',
        'Customs clearance and documentation handling',
        'Flexible scheduling to meet your timeline'
      ],
      icon: TruckIcon,
    },
    {
      title: 'Motorcycles',
      description: 'All types of motorcycles and scooters',
      features: [
        'Specialized motorcycle carriers',
        'Secure tie-down systems',
        'Climate-controlled transport',
        'Expert handling procedures',
        'Custom crating if needed'
      ],
      icon: TruckIcon,
    },
    {
      title: 'Classic & Luxury Cars',
      description: 'High-value and vintage automobiles',
      features: [
        'White-glove service',
        'Enclosed transport mandatory',
        'Extra insurance coverage',
        'Detailed documentation',
        'Climate-controlled environment'
      ],
      icon: ShieldCheckIcon,
    },
    {
      title: 'Commercial Vehicles',
      description: 'Vans, trucks, and commercial equipment',
      features: [
        'Heavy-duty transport solutions',
        'Permit handling',
        'Route planning',
        'Specialized equipment',
        'Business continuity focus'
      ],
      icon: DocumentTextIcon,
    },
  ];

  const process = [
    {
      step: '01',
      title: 'Vehicle Assessment',
      description: 'Detailed inspection and documentation of your vehicle\'s condition.',
      details: 'Our certified inspectors document every aspect of your vehicle, including photos, mileage, and any existing damage for insurance purposes.'
    },
    {
      step: '02',
      title: 'Documentation',
      description: 'Complete all necessary paperwork and customs documentation.',
      details: 'We handle all import/export documentation, customs declarations, and ensure compliance with destination country regulations.'
    },
    {
      step: '03',
      title: 'Secure Loading',
      description: 'Professional loading using specialized equipment and techniques.',
      details: 'Your vehicle is loaded using proper equipment and secured with industry-standard tie-downs and protective materials.'
    },
    {
      step: '04',
      title: 'Transit & Tracking',
      description: 'Safe transport with real-time tracking and regular updates.',
      details: 'Monitor your vehicle\'s journey with our tracking system and receive regular updates on transport progress.'
    },
    {
      step: '05',
      title: 'Delivery & Inspection',
      description: 'Safe delivery and final inspection at destination.',
      details: 'Upon arrival, we conduct a thorough inspection to ensure your vehicle arrived in the same condition as when shipped.'
    },
  ];

  const transportOptions = [
    {
      title: 'Open Transport',
      description: 'Cost-effective solution for standard vehicles',
      price: 'Most Economical',
      features: [
        'Multi-car carriers',
        'Suitable for daily drivers',
        'Weather exposure',
        'Standard insurance',
        'Faster scheduling'
      ]
    },
    {
      title: 'Enclosed Transport',
      description: 'Premium protection for valuable vehicles',
      price: 'Premium Service',
      features: [
        'Fully enclosed trailers',
        'Weather protection',
        'Enhanced security',
        'Higher insurance coverage',
        'Ideal for luxury cars'
      ]
    },
    {
      title: 'Container Shipping',
      description: 'International shipping in secure containers',
      price: 'International',
      features: [
        '20ft or 40ft containers',
        'Multiple vehicles per container',
        'Maximum security',
        'Customs handling included',
        'Port-to-port or door-to-door'
      ]
    }
  ];

  return (
    <Layout
      title="Vehicle Transportation Services - VayuVector"
      description="Professional international vehicle shipping services. Cars, motorcycles, and commercial vehicles transported safely worldwide with full insurance coverage."
    >
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-secondary-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <TruckIcon className="h-12 w-12 text-secondary-600" />
                <span className="text-secondary-600 font-semibold text-lg">Vehicle Transportation</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Safe & Secure Vehicle Shipping Worldwide
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8">
                Transport your vehicles internationally with confidence. From personal cars
                to luxury vehicles and motorcycles, we provide comprehensive shipping
                solutions with full insurance coverage. Our global network ensures safe,
                secure, and timely delivery to over 120 countries worldwide.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/quote" className="btn-secondary text-black font-semibold text-lg px-8 py-3">
                  Get Vehicle Quote
                </Link>
                <a href="tel:******-VAYU-VEC" className="btn-outline border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-black text-lg px-8 py-3">
                  Call Expert
                </a>
              </div>
            </div>
            <div className="aspect-video bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center relative overflow-hidden">
              {/* Car silhouettes background */}
              <div className="absolute inset-0 opacity-10">
                <div className="flex items-center justify-around h-full px-8">
                  <TruckIcon className="h-16 w-16 text-secondary-600 transform rotate-12" />
                  <TruckIcon className="h-20 w-20 text-secondary-600" />
                  <TruckIcon className="h-16 w-16 text-secondary-600 transform -rotate-12" />
                </div>
              </div>
              <div className="relative z-10 text-center">
                <TruckIcon className="h-24 w-24 text-secondary-600 opacity-70 mx-auto mb-4" />
                <div className="text-secondary-700 font-semibold">Global Vehicle Transport</div>
              </div>
              <TruckIcon className="h-32 w-32 text-secondary-600 opacity-50" />
            </div>
          </div>
        </div>
      </section>

      {/* Vehicle Types */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Vehicles We Transport
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We handle all types of vehicles with specialized equipment and 
              expert care, ensuring safe delivery to any destination worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {vehicleTypes.map((vehicle, index) => {
              const IconComponent = vehicle.icon;
              return (
                <div key={index} className="card p-8">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-secondary-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {vehicle.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-6">
                    {vehicle.description}
                  </p>
                  <ul className="space-y-2">
                    {vehicle.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircleIcon className="h-5 w-5 text-secondary-600 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Transport Options */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Transport Options
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the transport method that best suits your vehicle type, 
              budget, and timeline requirements.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {transportOptions.map((option, index) => (
              <div key={index} className="card p-8 text-center">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {option.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {option.description}
                  </p>
                  <div className="inline-block bg-secondary-100 text-secondary-800 px-4 py-2 rounded-full text-sm font-medium">
                    {option.price}
                  </div>
                </div>
                <ul className="space-y-3 text-left">
                  {option.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-3">
                      <CheckCircleIcon className="h-5 w-5 text-secondary-600 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Vehicle Transport Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We follow a meticulous process to ensure your vehicle is transported 
              safely and arrives in perfect condition.
            </p>
          </div>

          <div className="space-y-8">
            {process.map((item, index) => (
              <div key={index} className="card p-8">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-center">
                  <div className="text-center lg:text-left">
                    <div className="w-16 h-16 bg-secondary-600 text-black rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4 text-xl font-bold">
                      {item.step}
                    </div>
                  </div>
                  <div className="lg:col-span-3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 mb-3">
                      {item.description}
                    </p>
                    <p className="text-sm text-gray-500">
                      {item.details}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="aspect-square bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-2xl flex items-center justify-center">
              <ShieldCheckIcon className="h-32 w-32 text-secondary-600 opacity-50" />
            </div>
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Trust Us With Your Vehicle?
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <ShieldCheckIcon className="h-8 w-8 text-secondary-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Comprehensive Insurance
                    </h3>
                    <p className="text-gray-600">
                      Full coverage insurance protects your vehicle throughout the entire transport process.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <MapPinIcon className="h-8 w-8 text-secondary-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Real-Time Tracking
                    </h3>
                    <p className="text-gray-600">
                      Monitor your vehicle's location and transport progress with our advanced tracking system.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <DocumentTextIcon className="h-8 w-8 text-secondary-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Complete Documentation
                    </h3>
                    <p className="text-gray-600">
                      We handle all customs paperwork and ensure compliance with international regulations.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-900 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Ship Your Vehicle?
          </h2>
          <p className="text-lg mb-8 text-gray-300 max-w-2xl mx-auto">
            Get a personalized quote for your vehicle transportation. Our experts 
            will help you choose the best shipping method for your needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/quote" className="btn bg-secondary-600 text-black hover:bg-secondary-700 text-lg px-8 py-3 font-semibold">
              Get Vehicle Quote
            </Link>
            <a href="tel:******-VAYU-VEC" className="btn border-2 border-secondary-600 text-secondary-600 hover:bg-secondary-600 hover:text-black text-lg px-8 py-3">
              <PhoneIcon className="h-5 w-5 inline mr-2" />
              Call ******-VAYU-VEC
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default VehicleTransportationPage;
