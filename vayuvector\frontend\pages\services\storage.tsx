import React from 'react';
import Link from 'next/link';
import Layout from '@/components/Layout';
import { 
  ArchiveBoxIcon,
  ShieldCheckIcon,
  ClockIcon,
  ThermometerIcon,
  CameraIcon,
  KeyIcon,
  TruckIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const StorageSolutionsPage: React.FC = () => {
  const storageOptions = [
    {
      title: 'Short-Term Storage',
      description: 'Flexible storage solutions for temporary needs during your relocation process.',
      duration: '1-6 months',
      features: [
        'Climate-controlled facilities',
        'Easy access scheduling',
        'Inventory management system',
        'Insurance coverage included',
        'Flexible pickup/delivery',
        'Monthly billing options',
        'Online account management',
        '24/7 security monitoring'
      ],
      icon: ClockIcon,
      popular: false,
    },
    {
      title: 'Long-Term Storage',
      description: 'Secure storage for extended periods with cost-effective pricing.',
      duration: '6+ months',
      features: [
        'Premium climate control',
        'Dedicated storage units',
        'Comprehensive inventory',
        'Full replacement insurance',
        'Scheduled inspections',
        'Discounted long-term rates',
        'Priority access scheduling',
        'Advanced security systems'
      ],
      icon: ArchiveBoxIcon,
      popular: true,
    },
    {
      title: 'Specialty Storage',
      description: 'Specialized storage for valuable, fragile, or unique items.',
      duration: 'As needed',
      features: [
        'Temperature & humidity control',
        'Custom crating services',
        'Art & antique handling',
        'Wine storage facilities',
        'Document preservation',
        'High-value item security',
        'White-glove handling',
        'Conservation-grade environment'
      ],
      icon: ShieldCheckIcon,
      popular: false,
    },
  ];

  const securityFeatures = [
    {
      title: '24/7 Security Monitoring',
      description: 'Round-the-clock surveillance and monitoring systems',
      icon: CameraIcon,
    },
    {
      title: 'Climate Control',
      description: 'Optimal temperature and humidity control for all items',
      icon: ThermometerIcon,
    },
    {
      title: 'Access Control',
      description: 'Secure key card access with detailed entry logging',
      icon: KeyIcon,
    },
    {
      title: 'Insurance Coverage',
      description: 'Comprehensive insurance protection for stored items',
      icon: ShieldCheckIcon,
    },
  ];

  const benefits = [
    'State-of-the-art security systems',
    'Climate-controlled environments',
    'Flexible access schedules',
    'Comprehensive insurance coverage',
    'Professional inventory management',
    'Convenient pickup and delivery',
    'Online account management',
    'Competitive pricing options'
  ];

  return (
    <Layout
      title="Storage Solutions - VayuVector"
      description="Secure short-term and long-term storage solutions for international relocations. Climate-controlled facilities with comprehensive security and flexible access options."
    >
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-purple-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <ArchiveBoxIcon className="h-12 w-12 text-purple-600" />
                <span className="text-purple-600 font-semibold text-lg">Storage Solutions</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Secure Storage Solutions for Your Belongings
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8">
                Safe, secure, and convenient storage options for your international relocation. 
                From short-term transitions to long-term storage needs, we provide 
                climate-controlled facilities with comprehensive security.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/quote" className="btn bg-purple-600 text-white hover:bg-purple-700 font-semibold text-lg px-8 py-3">
                  Get Storage Quote
                </Link>
                <a href="tel:******-VAYU-VEC" className="btn-outline border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white text-lg px-8 py-3">
                  Call Storage Expert
                </a>
              </div>
            </div>
            <div className="aspect-video bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center relative overflow-hidden">
              {/* Storage boxes background */}
              <div className="absolute inset-0 opacity-10">
                <div className="grid grid-cols-3 gap-2 p-4 h-full">
                  {[...Array(9)].map((_, i) => (
                    <div key={i} className="bg-purple-600 rounded opacity-40"></div>
                  ))}
                </div>
              </div>
              <div className="relative z-10 text-center">
                <ArchiveBoxIcon className="h-24 w-24 text-purple-600 opacity-70 mx-auto mb-4" />
                <div className="text-purple-700 font-semibold">Secure Storage Solutions</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Storage Options */}
      <section className="section">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Flexible Storage Options
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the storage solution that best fits your timeline and needs. 
              All options include our premium security and climate control features.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {storageOptions.map((option, index) => {
              const IconComponent = option.icon;
              return (
                <div key={index} className={`card p-8 relative ${option.popular ? 'ring-2 ring-purple-600' : ''}`}>
                  {option.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                        Most Popular
                      </span>
                    </div>
                  )}
                  
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-purple-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{option.title}</h3>
                    <p className="text-purple-600 font-semibold mb-2">{option.duration}</p>
                    <p className="text-gray-600 text-sm">{option.description}</p>
                  </div>

                  <div className="space-y-3">
                    {option.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircleIcon className="h-4 w-4 text-purple-600 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8">
                    <Link 
                      href="/quote" 
                      className={`btn w-full text-center ${
                        option.popular 
                          ? 'bg-purple-600 text-white hover:bg-purple-700' 
                          : 'btn-outline border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white'
                      }`}
                    >
                      Get Quote
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Security Features */}
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Advanced Security & Protection
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Your belongings are protected by state-of-the-art security systems 
              and comprehensive insurance coverage.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {securityFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="section">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="aspect-square bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center">
              <TruckIcon className="h-32 w-32 text-purple-600 opacity-50" />
            </div>
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose Our Storage Solutions?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Our storage facilities are designed specifically for international relocations, 
                providing the security, accessibility, and peace of mind you need during your transition.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircleIcon className="h-5 w-5 text-purple-600 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-primary-900 text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Need Secure Storage for Your Move?
          </h2>
          <p className="text-lg mb-8 text-gray-300 max-w-2xl mx-auto">
            Get a personalized storage solution quote based on your specific needs. 
            Our storage experts will help you choose the perfect option.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/quote" className="btn bg-purple-600 text-white hover:bg-purple-700 text-lg px-8 py-3 font-semibold">
              Get Storage Quote
            </Link>
            <a href="tel:******-VAYU-VEC" className="btn border-2 border-white text-white hover:bg-white hover:text-primary-900 text-lg px-8 py-3">
              Call ******-VAYU-VEC
            </a>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default StorageSolutionsPage;
