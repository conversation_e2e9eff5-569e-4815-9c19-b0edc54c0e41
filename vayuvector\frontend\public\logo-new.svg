<svg width="320" height="100" viewBox="0 0 320 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="blackGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </linearGradient>

    <radialGradient id="glowEffect" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:0" />
    </radialGradient>

    <!-- Drop shadow filter -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- Glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background glow -->
  <circle cx="50" cy="50" r="45" fill="url(#glowEffect)" opacity="0.6"/>

  <!-- Main logo circle -->
  <circle cx="50" cy="50" r="40" fill="url(#goldGradient)" stroke="url(#blackGradient)" stroke-width="3" filter="url(#dropShadow)"/>

  <!-- Inner decorative ring -->
  <circle cx="50" cy="50" r="32" fill="none" stroke="#000000" stroke-width="1" opacity="0.3"/>

  <!-- Dynamic wind/movement lines with animation -->
  <g opacity="0.8">
    <path d="M20 30 Q30 25 40 30 Q50 35 60 30 Q70 25 80 30" stroke="#000000" stroke-width="2.5" fill="none" stroke-linecap="round">
      <animate attributeName="d"
               values="M20 30 Q30 25 40 30 Q50 35 60 30 Q70 25 80 30;M20 30 Q30 35 40 30 Q50 25 60 30 Q70 35 80 30;M20 30 Q30 25 40 30 Q50 35 60 30 Q70 25 80 30"
               dur="3s"
               repeatCount="indefinite"/>
    </path>
    <path d="M20 40 Q30 35 40 40 Q50 45 60 40 Q70 35 80 40" stroke="#000000" stroke-width="2.5" fill="none" stroke-linecap="round">
      <animate attributeName="d"
               values="M20 40 Q30 35 40 40 Q50 45 60 40 Q70 35 80 40;M20 40 Q30 45 40 40 Q50 35 60 40 Q70 45 80 40;M20 40 Q30 35 40 40 Q50 45 60 40 Q70 35 80 40"
               dur="3s"
               repeatCount="indefinite"
               begin="0.5s"/>
    </path>
    <path d="M20 50 Q30 45 40 50 Q50 55 60 50 Q70 45 80 50" stroke="#000000" stroke-width="2.5" fill="none" stroke-linecap="round">
      <animate attributeName="d"
               values="M20 50 Q30 45 40 50 Q50 55 60 50 Q70 45 80 50;M20 50 Q30 55 40 50 Q50 45 60 50 Q70 55 80 50;M20 50 Q30 45 40 50 Q50 55 60 50 Q70 45 80 50"
               dur="3s"
               repeatCount="indefinite"
               begin="1s"/>
    </path>
    <path d="M20 60 Q30 55 40 60 Q50 65 60 60 Q70 55 80 60" stroke="#000000" stroke-width="2.5" fill="none" stroke-linecap="round">
      <animate attributeName="d"
               values="M20 60 Q30 55 40 60 Q50 65 60 60 Q70 55 80 60;M20 60 Q30 65 40 60 Q50 55 60 60 Q70 65 80 60;M20 60 Q30 55 40 60 Q50 65 60 60 Q70 55 80 60"
               dur="3s"
               repeatCount="indefinite"
               begin="1.5s"/>
    </path>
  </g>

  <!-- Central vector arrow -->
  <g filter="url(#glow)">
    <path d="M30 50 L65 50 M58 43 L65 50 L58 57" stroke="#000000" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <circle cx="30" cy="50" r="3" fill="#000000"/>
  </g>

  <!-- Company Name with enhanced typography -->
  <text x="110" y="42" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#blackGradient)" filter="url(#dropShadow)">VayuVector</text>
  <text x="110" y="62" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#333333">Global Relocation Partner</text>

  <!-- Enhanced decorative elements -->
  <g opacity="0.7">
    <circle cx="85" cy="20" r="3" fill="url(#goldGradient)">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="92" cy="25" r="2" fill="#FFC107">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="98" cy="18" r="1.5" fill="url(#goldGradient)">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1s"/>
    </circle>

    <circle cx="290" cy="75" r="3" fill="url(#goldGradient)">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1.5s"/>
    </circle>
    <circle cx="297" cy="80" r="2" fill="#FFC107">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.3s"/>
    </circle>
    <circle cx="304" cy="72" r="1.5" fill="url(#goldGradient)">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.8s"/>
    </circle>
  </g>

  <!-- Subtle background pattern -->
  <g opacity="0.1">
    <path d="M0 20 Q160 10 320 20" stroke="url(#goldGradient)" stroke-width="1" fill="none"/>
    <path d="M0 80 Q160 90 320 80" stroke="url(#goldGradient)" stroke-width="1" fill="none"/>
  </g>
</svg>
