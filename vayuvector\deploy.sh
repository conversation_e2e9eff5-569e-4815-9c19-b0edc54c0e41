#!/bin/bash

# VayuVector Deployment Script
# This script helps deploy the VayuVector application using Docker

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and <PERSON>er Compose are installed"
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        if [ -f .env.example ]; then
            cp .env.example .env
        else
            print_error "No .env.example file found. Please create a .env file manually."
            exit 1
        fi
    fi
    print_success ".env file exists"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs/nginx
    mkdir -p ssl
    
    print_success "Directories created"
}

# Function to build and start services
deploy_development() {
    print_status "Deploying in DEVELOPMENT mode..."
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker-compose down 2>/dev/null || true
    
    # Build and start services
    print_status "Building and starting services..."
    docker-compose up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_services_health
    
    print_success "Development deployment completed!"
    print_status "Access your application at:"
    echo "  - Frontend: http://localhost:3000"
    echo "  - Backend API: http://localhost:5000"
    echo "  - Nginx Proxy: http://localhost:80"
}

# Function to deploy in production
deploy_production() {
    print_status "Deploying in PRODUCTION mode..."
    
    # Check if production env file exists
    if [ ! -f .env.production ]; then
        print_error ".env.production file not found. Please create it first."
        print_status "You can copy .env to .env.production and update the values:"
        echo "  cp .env .env.production"
        exit 1
    fi
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Build and start services
    print_status "Building and starting production services..."
    docker-compose -f docker-compose.prod.yml --env-file .env.production up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 45
    
    # Check service health
    check_services_health_prod
    
    print_success "Production deployment completed!"
    print_status "Your application should be accessible at your configured domain"
}

# Function to check service health (development)
check_services_health() {
    print_status "Checking service health..."
    
    # Check if containers are running
    if ! docker-compose ps | grep -q "Up"; then
        print_error "Some services are not running properly"
        docker-compose logs --tail=50
        exit 1
    fi
    
    # Check backend health
    for i in {1..10}; do
        if curl -f http://localhost:5000/api/health &>/dev/null; then
            print_success "Backend is healthy"
            break
        fi
        if [ $i -eq 10 ]; then
            print_error "Backend health check failed"
            docker-compose logs backend --tail=20
            exit 1
        fi
        sleep 5
    done
    
    # Check frontend
    for i in {1..10}; do
        if curl -f http://localhost:3000 &>/dev/null; then
            print_success "Frontend is healthy"
            break
        fi
        if [ $i -eq 10 ]; then
            print_error "Frontend health check failed"
            docker-compose logs frontend --tail=20
            exit 1
        fi
        sleep 5
    done
    
    print_success "All services are healthy!"
}

# Function to check service health (production)
check_services_health_prod() {
    print_status "Checking production service health..."
    
    # Check if containers are running
    if ! docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        print_error "Some services are not running properly"
        docker-compose -f docker-compose.prod.yml logs --tail=50
        exit 1
    fi
    
    print_success "Production services are running!"
}

# Function to show logs
show_logs() {
    if [ "$1" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml logs -f
    else
        docker-compose logs -f
    fi
}

# Function to stop services
stop_services() {
    if [ "$1" = "prod" ]; then
        print_status "Stopping production services..."
        docker-compose -f docker-compose.prod.yml down
    else
        print_status "Stopping development services..."
        docker-compose down
    fi
    print_success "Services stopped"
}

# Function to show status
show_status() {
    if [ "$1" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
}

# Function to backup database
backup_database() {
    print_status "Creating database backup..."
    
    BACKUP_DIR="backups"
    mkdir -p $BACKUP_DIR
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/mongodb_backup_$TIMESTAMP.tar.gz"
    
    if [ "$1" = "prod" ]; then
        CONTAINER_NAME="vayuvector-mongodb-prod"
    else
        CONTAINER_NAME="vayuvector-mongodb"
    fi
    
    docker exec $CONTAINER_NAME mongodump --out /tmp/backup
    docker cp $CONTAINER_NAME:/tmp/backup ./backup_temp
    tar -czf $BACKUP_FILE backup_temp
    rm -rf backup_temp
    
    print_success "Database backup created: $BACKUP_FILE"
}

# Main script logic
case "$1" in
    "dev"|"development")
        check_docker
        check_env_file
        create_directories
        deploy_development
        ;;
    "prod"|"production")
        check_docker
        create_directories
        deploy_production
        ;;
    "logs")
        show_logs $2
        ;;
    "stop")
        stop_services $2
        ;;
    "status")
        show_status $2
        ;;
    "backup")
        backup_database $2
        ;;
    "help"|"--help"|"-h")
        echo "VayuVector Deployment Script"
        echo ""
        echo "Usage: $0 [COMMAND] [OPTIONS]"
        echo ""
        echo "Commands:"
        echo "  dev, development    Deploy in development mode"
        echo "  prod, production    Deploy in production mode"
        echo "  logs [prod]         Show logs (add 'prod' for production)"
        echo "  stop [prod]         Stop services (add 'prod' for production)"
        echo "  status [prod]       Show service status (add 'prod' for production)"
        echo "  backup [prod]       Backup database (add 'prod' for production)"
        echo "  help                Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 dev              # Deploy in development mode"
        echo "  $0 prod             # Deploy in production mode"
        echo "  $0 logs             # Show development logs"
        echo "  $0 logs prod        # Show production logs"
        echo "  $0 stop             # Stop development services"
        echo "  $0 backup prod      # Backup production database"
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' to see available commands"
        exit 1
        ;;
esac
