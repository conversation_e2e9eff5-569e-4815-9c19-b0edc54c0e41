# VayuVector - Quick Start with Docker 🚀

Get your VayuVector application up and running in minutes with Dock<PERSON>!

## 🎯 Prerequisites

Make sure you have these installed:
- **Docker Desktop** (includes Docker Compose)
- **Git** (optional, if cloning)

### Install Docker Desktop
- **Windows/Mac**: Download from [docker.com](https://www.docker.com/products/docker-desktop/)
- **Linux**: Follow the [official installation guide](https://docs.docker.com/engine/install/)

## ⚡ Super Quick Start (Development)

### Option 1: Using the Deployment Script (Recommended)

**Windows:**
```cmd
# Navigate to your project directory
cd vayuvector

# Deploy in development mode
deploy.bat dev
```

**Linux/Mac:**
```bash
# Navigate to your project directory
cd vayuvector

# Make script executable (Linux/Mac only)
chmod +x deploy.sh

# Deploy in development mode
./deploy.sh dev
```

### Option 2: Manual Docker Commands

```bash
# Navigate to project directory
cd vayuvector

# Start all services
docker-compose up --build -d

# Check if services are running
docker-compose ps
```

## 🌐 Access Your Application

After deployment, access your application at:

- **🖥️ Frontend (Website)**: http://localhost:3000
- **🔧 Backend API**: http://localhost:5000
- **🌍 Nginx Proxy**: http://localhost:80
- **📊 MongoDB**: localhost:27017 (for database tools)
- **⚡ Redis**: localhost:6379 (for caching)

## 🛠️ Common Commands

### Using Deployment Scripts

**Windows (deploy.bat):**
```cmd
deploy.bat dev          # Start development
deploy.bat logs         # View logs
deploy.bat status       # Check status
deploy.bat stop         # Stop services
deploy.bat help         # Show help
```

**Linux/Mac (deploy.sh):**
```bash
./deploy.sh dev         # Start development
./deploy.sh logs        # View logs
./deploy.sh status      # Check status
./deploy.sh stop        # Stop services
./deploy.sh help        # Show help
```

### Using Docker Compose Directly

```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild and restart
docker-compose up --build -d

# Check service status
docker-compose ps
```

## 🔧 Configuration

### Environment Variables

The application uses the `.env` file for configuration. Key variables:

```env
# Database
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=password123

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:5000/api

# Optional: Add your API keys
GOOGLE_MAPS_API_KEY=your-key-here
STRIPE_SECRET_KEY=your-key-here
```

### Customization

1. **Update Logo**: Replace files in `frontend/public/`
2. **Modify Colors**: Edit `frontend/tailwind.config.js`
3. **Add Content**: Update pages in `frontend/pages/`
4. **API Changes**: Modify files in `backend/`

## 🚀 Production Deployment

### 1. Create Production Environment
```bash
# Copy environment file
cp .env .env.production

# Edit with production values
# - Change passwords
# - Add real API keys
# - Set production URLs
```

### 2. Deploy to Production
```bash
# Using deployment script
./deploy.sh prod        # Linux/Mac
deploy.bat prod         # Windows

# Or manually
docker-compose -f docker-compose.prod.yml --env-file .env.production up --build -d
```

## 🔍 Troubleshooting

### Services Won't Start
```bash
# Check what's using the ports
netstat -tulpn | grep :3000  # Linux/Mac
netstat -an | findstr :3000  # Windows

# View detailed logs
docker-compose logs -f [service-name]
```

### Database Issues
```bash
# Restart MongoDB
docker-compose restart mongodb

# Check MongoDB logs
docker-compose logs mongodb
```

### Build Failures
```bash
# Clean Docker cache
docker system prune -a

# Rebuild from scratch
docker-compose build --no-cache
docker-compose up -d
```

### Port Conflicts
If ports 3000, 5000, or 80 are in use, you can change them in `docker-compose.yml`:

```yaml
services:
  frontend:
    ports:
      - "3001:3000"  # Change 3000 to 3001
  backend:
    ports:
      - "5001:5000"  # Change 5000 to 5001
```

## 📊 Monitoring

### Check Service Health
```bash
# All services status
docker-compose ps

# Resource usage
docker stats

# Backend health check
curl http://localhost:5000/api/health
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f frontend
docker-compose logs -f backend
docker-compose logs -f mongodb
```

## 🔒 Security Notes

For production deployment:

1. **Change default passwords** in `.env.production`
2. **Use strong JWT secrets**
3. **Add SSL certificates** to `ssl/` directory
4. **Update domain names** in nginx configuration
5. **Enable firewall** and limit exposed ports

## 📚 Next Steps

1. **Customize Content**: Update service pages and content
2. **Add SSL**: Configure HTTPS for production
3. **Set up Domain**: Point your domain to the server
4. **Configure Monitoring**: Set up logging and monitoring
5. **Backup Strategy**: Implement regular database backups

## 🆘 Need Help?

1. **Check Logs**: `docker-compose logs -f`
2. **Verify Services**: `docker-compose ps`
3. **Review Documentation**: See `DOCKER_DEPLOYMENT_GUIDE.md`
4. **Common Issues**: Check the troubleshooting section above

## 🎉 Success!

If you can access http://localhost:3000 and see the VayuVector website, congratulations! Your application is running successfully.

---

**Happy Coding! 🚀**
