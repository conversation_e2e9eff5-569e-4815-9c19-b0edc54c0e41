@echo off
REM VayuVector Deployment Script for Windows
REM This script helps deploy the VayuVector application using Docker

setlocal enabledelayedexpansion

REM Function to print colored output (simplified for Windows)
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Function to check if Docker is installed
:check_docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not installed. Please install Docker Desktop first."
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose is not installed. Please install Docker Desktop first."
    exit /b 1
)

call :print_success "Docker and Docker Compose are installed"
goto :eof

REM Function to check if .env file exists
:check_env_file
if not exist .env (
    call :print_warning ".env file not found. Please create one manually."
    call :print_status "You can copy the existing .env file and modify the values as needed."
)
call :print_success ".env file exists"
goto :eof

REM Function to create necessary directories
:create_directories
call :print_status "Creating necessary directories..."

if not exist logs mkdir logs
if not exist logs\nginx mkdir logs\nginx
if not exist ssl mkdir ssl

call :print_success "Directories created"
goto :eof

REM Function to build and start services
:deploy_development
call :print_status "Deploying in DEVELOPMENT mode..."

REM Stop existing containers
call :print_status "Stopping existing containers..."
docker-compose down 2>nul

REM Build and start services
call :print_status "Building and starting services..."
docker-compose up --build -d

REM Wait for services to be ready
call :print_status "Waiting for services to be ready..."
timeout /t 30 /nobreak >nul

call :print_success "Development deployment completed!"
call :print_status "Access your application at:"
echo   - Frontend: http://localhost:3000
echo   - Backend API: http://localhost:5000
echo   - Nginx Proxy: http://localhost:80
goto :eof

REM Function to deploy in production
:deploy_production
call :print_status "Deploying in PRODUCTION mode..."

REM Check if production env file exists
if not exist .env.production (
    call :print_error ".env.production file not found. Please create it first."
    call :print_status "You can copy .env to .env.production and update the values:"
    echo   copy .env .env.production
    exit /b 1
)

REM Stop existing containers
call :print_status "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down 2>nul

REM Build and start services
call :print_status "Building and starting production services..."
docker-compose -f docker-compose.prod.yml --env-file .env.production up --build -d

REM Wait for services to be ready
call :print_status "Waiting for services to be ready..."
timeout /t 45 /nobreak >nul

call :print_success "Production deployment completed!"
call :print_status "Your application should be accessible at your configured domain"
goto :eof

REM Function to show logs
:show_logs
if "%~1"=="prod" (
    docker-compose -f docker-compose.prod.yml logs -f
) else (
    docker-compose logs -f
)
goto :eof

REM Function to stop services
:stop_services
if "%~1"=="prod" (
    call :print_status "Stopping production services..."
    docker-compose -f docker-compose.prod.yml down
) else (
    call :print_status "Stopping development services..."
    docker-compose down
)
call :print_success "Services stopped"
goto :eof

REM Function to show status
:show_status
if "%~1"=="prod" (
    docker-compose -f docker-compose.prod.yml ps
) else (
    docker-compose ps
)
goto :eof

REM Function to show help
:show_help
echo VayuVector Deployment Script for Windows
echo.
echo Usage: %~nx0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   dev, development    Deploy in development mode
echo   prod, production    Deploy in production mode
echo   logs [prod]         Show logs (add 'prod' for production)
echo   stop [prod]         Stop services (add 'prod' for production)
echo   status [prod]       Show service status (add 'prod' for production)
echo   help                Show this help message
echo.
echo Examples:
echo   %~nx0 dev              # Deploy in development mode
echo   %~nx0 prod             # Deploy in production mode
echo   %~nx0 logs             # Show development logs
echo   %~nx0 logs prod        # Show production logs
echo   %~nx0 stop             # Stop development services
goto :eof

REM Main script logic
if "%~1"=="dev" goto :main_dev
if "%~1"=="development" goto :main_dev
if "%~1"=="prod" goto :main_prod
if "%~1"=="production" goto :main_prod
if "%~1"=="logs" goto :main_logs
if "%~1"=="stop" goto :main_stop
if "%~1"=="status" goto :main_status
if "%~1"=="help" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-h" goto :show_help
if "%~1"=="" goto :show_help

call :print_error "Unknown command: %~1"
echo Use '%~nx0 help' to see available commands
exit /b 1

:main_dev
call :check_docker
call :check_env_file
call :create_directories
call :deploy_development
goto :eof

:main_prod
call :check_docker
call :create_directories
call :deploy_production
goto :eof

:main_logs
call :show_logs %~2
goto :eof

:main_stop
call :stop_services %~2
goto :eof

:main_status
call :show_status %~2
goto :eof
