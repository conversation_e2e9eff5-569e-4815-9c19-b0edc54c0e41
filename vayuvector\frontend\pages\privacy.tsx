import React from 'react';
import Layout from '@/components/Layout';
import { ShieldCheckIcon, EyeIcon, LockClosedIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

const PrivacyPolicyPage: React.FC = () => {
  const lastUpdated = "December 1, 2024";

  const sections = [
    {
      title: "Information We Collect",
      icon: DocumentTextIcon,
      content: [
        "Personal identification information (name, email address, phone number)",
        "Moving details (origin, destination, inventory information)",
        "Payment information (processed securely through third-party providers)",
        "Communication records (emails, chat logs, support tickets)",
        "Website usage data (cookies, IP addresses, browser information)"
      ]
    },
    {
      title: "How We Use Your Information",
      icon: EyeIcon,
      content: [
        "Provide and improve our relocation services",
        "Process quotes and bookings",
        "Communicate with you about your move",
        "Send service updates and important notifications",
        "Comply with legal and regulatory requirements",
        "Prevent fraud and ensure security"
      ]
    },
    {
      title: "Information Sharing",
      icon: ShieldCheckIcon,
      content: [
        "We do not sell, trade, or rent your personal information",
        "Information may be shared with trusted service partners",
        "Data may be disclosed when required by law",
        "Anonymous, aggregated data may be used for analytics",
        "All third parties are bound by confidentiality agreements"
      ]
    },
    {
      title: "Data Security",
      icon: LockClosedIcon,
      content: [
        "Industry-standard encryption for data transmission",
        "Secure servers with regular security updates",
        "Limited access to personal information",
        "Regular security audits and assessments",
        "Incident response procedures in place"
      ]
    }
  ];

  return (
    <Layout
      title="Privacy Policy - VayuVector"
      description="VayuVector's privacy policy outlines how we collect, use, and protect your personal information during your international relocation."
    >
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-gray-50 to-primary-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <ShieldCheckIcon className="h-12 w-12 text-primary-600" />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                Privacy Policy
              </h1>
            </div>
            <p className="text-lg md:text-xl text-gray-600 mb-4">
              Your privacy is important to us. This policy explains how we collect, 
              use, and protect your personal information.
            </p>
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated}
            </p>
          </div>
        </div>
      </section>

      {/* Introduction */}
      <section className="section">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="card p-8 mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Introduction</h2>
              <p className="text-gray-600 mb-4">
                VayuVector ("we," "our," or "us") is committed to protecting your privacy and ensuring 
                the security of your personal information. This Privacy Policy explains how we collect, 
                use, disclose, and safeguard your information when you use our services or visit our website.
              </p>
              <p className="text-gray-600">
                By using our services, you consent to the collection and use of your information 
                as described in this policy. If you do not agree with this policy, please do not 
                use our services.
              </p>
            </div>

            {/* Policy Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => {
                const IconComponent = section.icon;
                return (
                  <div key={index} className="card p-8">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="h-6 w-6 text-primary-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                    </div>
                    <ul className="space-y-3">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>

            {/* Additional Sections */}
            <div className="space-y-8 mt-12">
              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Your Rights</h2>
                <p className="text-gray-600 mb-4">You have the right to:</p>
                <ul className="space-y-2 text-gray-700">
                  <li>• Access your personal information</li>
                  <li>• Correct inaccurate information</li>
                  <li>• Request deletion of your data</li>
                  <li>• Opt-out of marketing communications</li>
                  <li>• File a complaint with regulatory authorities</li>
                </ul>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Cookies and Tracking</h2>
                <p className="text-gray-600 mb-4">
                  We use cookies and similar technologies to enhance your experience on our website. 
                  These technologies help us understand how you use our site and improve our services.
                </p>
                <p className="text-gray-600">
                  You can control cookie settings through your browser preferences. However, 
                  disabling cookies may affect the functionality of our website.
                </p>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">International Transfers</h2>
                <p className="text-gray-600 mb-4">
                  As a global relocation company, we may transfer your information to countries 
                  outside your residence. We ensure appropriate safeguards are in place to 
                  protect your information during such transfers.
                </p>
              </div>

              <div className="card p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Changes to This Policy</h2>
                <p className="text-gray-600 mb-4">
                  We may update this Privacy Policy from time to time. We will notify you of 
                  any changes by posting the new policy on this page and updating the 
                  "Last updated" date.
                </p>
                <p className="text-gray-600">
                  We encourage you to review this policy periodically to stay informed about 
                  how we protect your information.
                </p>
              </div>

              <div className="card p-8 bg-primary-50 border-primary-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Us</h2>
                <p className="text-gray-600 mb-4">
                  If you have any questions about this Privacy Policy or our data practices, 
                  please contact us:
                </p>
                <div className="space-y-2 text-gray-700">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> 2441 Saki Naka, Andheri East, Mumbai, India</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default PrivacyPolicyPage;
