# VayuVector Docker Deployment Guide

This guide will help you deploy the VayuVector application using Docker and Docker Compose.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Docker** (version 20.10 or higher)
- **Docker Compose** (version 2.0 or higher)
- **Git** (to clone the repository if needed)

### Install Docker and Docker Compose

#### On Windows:
1. Download and install Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop/)
2. Docker Compose is included with Docker Desktop

#### On macOS:
1. Download and install Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop/)
2. Docker Compose is included with Docker Desktop

#### On Linux (Ubuntu/Debian):
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## Quick Start (Development)

### 1. Clone and Navigate to Project
```bash
git clone <your-repository-url>
cd vayuvector
```

### 2. Environment Setup
The `.env` file is already configured with development defaults. For production, you'll need to update the values.

### 3. Build and Start Services
```bash
# Build and start all services
docker-compose up --build

# Or run in detached mode (background)
docker-compose up --build -d
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Nginx Proxy**: http://localhost:80
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379

### 5. Stop Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete your database data)
docker-compose down -v
```

## Production Deployment

### 1. Environment Configuration
Create a production environment file:

```bash
cp .env .env.production
```

Edit `.env.production` with your production values:

```env
# Application Environment
NODE_ENV=production
BACKEND_PORT=5000

# Database Configuration (Use strong passwords!)
MONGO_ROOT_USERNAME=your_secure_username
MONGO_ROOT_PASSWORD=your_very_secure_password
MONGO_DB_NAME=vayuvector_prod

# JWT Configuration (Generate a strong secret!)
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# API Keys (Replace with your actual keys)
WHATSAPP_API_KEY=your-whatsapp-api-key
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key

# Frontend Configuration
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_WHATSAPP_NUMBER=+1234567890
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Redis Configuration
REDIS_URL=redis://redis:6379
```

### 2. SSL Certificate Setup (Optional but Recommended)
If you have SSL certificates, place them in the `ssl/` directory:
```
ssl/
├── cert.pem
└── private.key
```

### 3. Deploy with Production Configuration
```bash
# Build and start with production configuration
docker-compose -f docker-compose.prod.yml --env-file .env.production up --build -d
```

### 4. Verify Deployment
```bash
# Check if all services are running
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs -f

# Check specific service logs
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f backend
```

## Service Architecture

The application consists of the following services:

1. **Frontend** (Next.js) - Port 3000
   - React-based user interface
   - Server-side rendering
   - Static asset serving

2. **Backend** (Node.js/Express) - Port 5000
   - REST API
   - Business logic
   - Database interactions

3. **MongoDB** - Port 27017
   - Primary database
   - Document storage
   - User data, quotes, etc.

4. **Redis** - Port 6379
   - Caching layer
   - Session storage
   - Rate limiting

5. **Nginx** - Ports 80/443
   - Reverse proxy
   - Load balancing
   - SSL termination
   - Static file serving

## Common Commands

### Development Commands
```bash
# Start services
docker-compose up

# Start in background
docker-compose up -d

# Rebuild specific service
docker-compose build frontend
docker-compose up frontend

# View logs
docker-compose logs -f

# Execute commands in running container
docker-compose exec frontend sh
docker-compose exec backend sh

# Stop services
docker-compose down
```

### Production Commands
```bash
# Start production services
docker-compose -f docker-compose.prod.yml up -d

# Update and restart services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Backup database
docker-compose -f docker-compose.prod.yml exec mongodb mongodump --out /data/backup

# View production logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Maintenance Commands
```bash
# Clean up unused Docker resources
docker system prune -a

# Remove all stopped containers
docker container prune

# Remove unused images
docker image prune -a

# Remove unused volumes (WARNING: This deletes data!)
docker volume prune
```

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep :3000

# Kill the process using the port
sudo kill -9 <PID>
```

#### 2. Permission Denied Errors
```bash
# Fix Docker permissions (Linux)
sudo usermod -aG docker $USER
newgrp docker
```

#### 3. Build Failures
```bash
# Clear Docker cache and rebuild
docker-compose down
docker system prune -a
docker-compose build --no-cache
docker-compose up
```

#### 4. Database Connection Issues
```bash
# Check MongoDB logs
docker-compose logs mongodb

# Restart MongoDB service
docker-compose restart mongodb
```

#### 5. Frontend Not Loading
```bash
# Check frontend logs
docker-compose logs frontend

# Rebuild frontend
docker-compose build frontend
docker-compose up frontend
```

### Health Checks

The application includes health checks for monitoring:

```bash
# Check backend health
curl http://localhost:5000/api/health

# Check all service status
docker-compose ps
```

## Monitoring and Logs

### Log Locations
- **Application logs**: Available via `docker-compose logs`
- **Nginx logs**: `./logs/nginx/` (in production)
- **MongoDB logs**: Available via `docker-compose logs mongodb`

### Monitoring Commands
```bash
# Real-time logs for all services
docker-compose logs -f

# Monitor resource usage
docker stats

# Check service health
docker-compose ps
```

## Security Considerations

1. **Change default passwords** in production
2. **Use strong JWT secrets**
3. **Enable SSL/HTTPS** for production
4. **Regularly update Docker images**
5. **Limit exposed ports** in production
6. **Use environment-specific configurations**
7. **Regular backups** of database

## Backup and Recovery

### Database Backup
```bash
# Create backup
docker-compose exec mongodb mongodump --out /data/backup

# Restore backup
docker-compose exec mongodb mongorestore /data/backup
```

### Full System Backup
```bash
# Backup volumes
docker run --rm -v vayuvector_mongodb_data:/data -v $(pwd):/backup alpine tar czf /backup/mongodb_backup.tar.gz /data
```

## Next Steps

After successful deployment:

1. **Configure DNS** to point to your server
2. **Set up SSL certificates** (Let's Encrypt recommended)
3. **Configure monitoring** (optional)
4. **Set up automated backups**
5. **Configure log rotation**
6. **Set up CI/CD pipeline** (optional)

## Support

If you encounter issues:

1. Check the logs: `docker-compose logs -f`
2. Verify all services are running: `docker-compose ps`
3. Check the troubleshooting section above
4. Review Docker and Docker Compose documentation

---

**Happy Deploying! 🚀**
