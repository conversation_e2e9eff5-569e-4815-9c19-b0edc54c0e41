import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  HomeIcon, 
  TruckIcon, 
  DocumentTextIcon,
  ArchiveBoxIcon,
  GlobeAltIcon,
  ShieldCheckIcon 
} from '@heroicons/react/24/outline';

interface Service {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  features: string[];
  color: string;
  bgColor: string;
}

const services: Service[] = [
  {
    id: 'residential',
    title: 'Residential Relocation',
    description: 'Complete household moving services with professional packing and careful handling of your belongings.',
    icon: HomeIcon,
    features: [
      'Professional packing & unpacking',
      'Furniture disassembly & assembly',
      'Appliance disconnection & reconnection',
      'Fragile item special handling',
      'Room-by-room organization',
    ],
    color: 'text-primary-600',
    bgColor: 'bg-primary-100',
  },
  {
    id: 'vehicle',
    title: 'Vehicle Transportation',
    description: 'Safe and secure transportation of your vehicles with full insurance coverage and tracking.',
    icon: TruckIcon,
    features: [
      'Door-to-door car shipping',
      'Motorcycle transportation',
      'Classic & luxury vehicle handling',
      'International documentation',
      'Real-time tracking',
    ],
    color: 'text-secondary-600',
    bgColor: 'bg-secondary-100',
  },
  {
    id: 'documentation',
    title: 'Documentation Support',
    description: 'Complete assistance with all paperwork, visas, and legal requirements for your move.',
    icon: DocumentTextIcon,
    features: [
      'Visa & work permit guidance',
      'Customs clearance assistance',
      'School enrollment support',
      'Banking setup coordination',
      'Local registration help',
    ],
    color: 'text-accent-600',
    bgColor: 'bg-accent-100',
  },
  {
    id: 'storage',
    title: 'Storage Solutions',
    description: 'Secure short-term and long-term storage options for your belongings during transition.',
    icon: ArchiveBoxIcon,
    features: [
      'Climate-controlled facilities',
      'Short & long-term options',
      'Inventory management',
      '24/7 security monitoring',
      'Easy access scheduling',
    ],
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
  {
    id: 'global',
    title: 'Global Network',
    description: 'Worldwide coverage with local expertise in over 120 countries.',
    icon: GlobeAltIcon,
    features: [
      '120+ countries served',
      'Local partner network',
      'Cultural expertise',
      'Multi-language support',
      'Time zone coordination',
    ],
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    id: 'insurance',
    title: 'Insurance & Protection',
    description: 'Comprehensive insurance coverage and protection for your valuable belongings.',
    icon: ShieldCheckIcon,
    features: [
      'Full replacement value coverage',
      'Transit insurance included',
      'Damage claim processing',
      'Third-party liability',
      'Peace of mind guarantee',
    ],
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
  },
];

const ServicesCarousel: React.FC = () => {
  const [activeService, setActiveService] = useState(0);

  return (
    <section className="section">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Comprehensive Relocation Services
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            From residential moves to vehicle transportation, we provide end-to-end 
            solutions for your international relocation needs.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Service Tabs */}
          <div className="space-y-4">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              const isActive = activeService === index;
              
              return (
                <motion.button
                  key={service.id}
                  onClick={() => setActiveService(index)}
                  className={`w-full text-left p-6 rounded-xl transition-all duration-300 ${
                    isActive
                      ? 'bg-white shadow-medium border-l-4 border-secondary-600'
                      : 'bg-gray-50 hover:bg-white hover:shadow-soft'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-12 h-12 rounded-lg ${service.bgColor} flex items-center justify-center`}>
                      <IconComponent className={`h-6 w-6 ${service.color}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className={`text-lg font-semibold mb-2 ${
                        isActive ? 'text-secondary-600' : 'text-gray-900'
                      }`}>
                        {service.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {service.description}
                      </p>
                    </div>
                  </div>
                </motion.button>
              );
            })}
          </div>

          {/* Service Details */}
          <div className="lg:pl-8">
            <motion.div
              key={activeService}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="card p-8"
            >
              <div className="flex items-center space-x-4 mb-6">
                <div className={`w-16 h-16 rounded-xl ${services[activeService].bgColor} flex items-center justify-center`}>
                  {React.createElement(services[activeService].icon, {
                    className: `h-8 w-8 ${services[activeService].color}`
                  })}
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">
                    {services[activeService].title}
                  </h3>
                  <p className="text-gray-600">
                    {services[activeService].description}
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900 mb-4">
                  What's Included:
                </h4>
                {services[activeService].features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <div className="w-2 h-2 bg-secondary-600 rounded-full flex-shrink-0"></div>
                    <span className="text-gray-700">{feature}</span>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <button className="btn-secondary w-full sm:w-auto text-black font-semibold">
                  Learn More About {services[activeService].title}
                </button>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Service Stats */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-primary-600 mb-2">
              50K+
            </div>
            <div className="text-gray-600 text-sm">Successful Moves</div>
          </div>
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-secondary-600 mb-2">
              120+
            </div>
            <div className="text-gray-600 text-sm">Countries Served</div>
          </div>
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-accent-600 mb-2">
              15+
            </div>
            <div className="text-gray-600 text-sm">Years Experience</div>
          </div>
          <div className="text-center">
            <div className="text-2xl md:text-3xl font-bold text-green-600 mb-2">
              24/7
            </div>
            <div className="text-gray-600 text-sm">Customer Support</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesCarousel;
