import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { Bars3Icon, XMarkIcon, PhoneIcon } from '@heroicons/react/24/outline';
import { ChevronDownIcon } from '@heroicons/react/20/solid';

const navigation = [
  { name: 'Home', href: '/' },
  {
    name: 'Services',
    href: '/services',
    children: [
      { name: 'Residential Relocation', href: '/services/residential' },
      { name: 'Vehicle Transportation', href: '/services/vehicle' },
      { name: 'Documentation Support', href: '/services/documentation' },
      { name: 'Storage Solutions', href: '/services/storage' },
    ],
  },
  { name: 'How It Works', href: '/how-it-works' },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },
];

const Header: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [servicesOpen, setServicesOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled
          ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-gray-100'
          : 'bg-gradient-to-r from-black/40 via-black/30 to-black/40 backdrop-blur-md'
      }`}
    >
      <nav className="container-custom">
        <div className="flex items-center justify-between h-20 lg:h-24">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="group flex items-center space-x-3 py-2 px-3 rounded-xl transition-all duration-300 hover:bg-white/10">
              <div className="relative">
                <Image
                  src="/logo-new.svg"
                  alt="VayuVector Logo"
                  width={320}
                  height={100}
                  className="h-12 lg:h-14 w-auto transition-transform duration-300 group-hover:scale-105"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-r from-accent-400/20 to-secondary-400/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl"></div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2">
            {navigation.map((item) => (
              <div key={item.name} className="relative">
                {item.children ? (
                  <div
                    className="relative"
                    onMouseEnter={() => setServicesOpen(true)}
                    onMouseLeave={() => setServicesOpen(false)}
                  >
                    <button
                      className={`group flex items-center space-x-2 px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${
                        isActive(item.href)
                          ? 'text-secondary-600 bg-secondary-50'
                          : scrolled
                          ? 'text-gray-700 hover:text-secondary-600 hover:bg-secondary-50'
                          : 'text-white hover:text-accent-300 hover:bg-white/10'
                      }`}
                    >
                      <span className="relative">
                        {item.name}
                        <span className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-400 to-secondary-400 transition-all duration-300 group-hover:w-full`}></span>
                      </span>
                      <ChevronDownIcon
                        className={`h-4 w-4 transition-all duration-300 ${
                          servicesOpen ? 'rotate-180 text-accent-400' : ''
                        }`}
                      />
                    </button>

                    {/* Dropdown Menu */}
                    {servicesOpen && (
                      <div className="absolute top-full left-0 mt-3 w-72 bg-white/98 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
                        <div className="px-3 py-2 border-b border-gray-100">
                          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Our Services</h3>
                        </div>
                        {item.children.map((child, index) => (
                          <Link
                            key={child.name}
                            href={child.href}
                            className="group flex items-center px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-secondary-50 hover:to-accent-50 hover:text-secondary-700 transition-all duration-300 border-l-4 border-transparent hover:border-secondary-400"
                          >
                            <div className="w-2 h-2 bg-gradient-to-r from-accent-400 to-secondary-400 rounded-full mr-3 opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <span className="relative">
                              {child.name}
                              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-400 to-secondary-400 transition-all duration-300 group-hover:w-full"></span>
                            </span>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={`group px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 ${
                      isActive(item.href)
                        ? 'text-secondary-600 bg-secondary-50'
                        : scrolled
                        ? 'text-gray-700 hover:text-secondary-600 hover:bg-secondary-50'
                        : 'text-white hover:text-accent-300 hover:bg-white/10'
                    }`}
                  >
                    <span className="relative">
                      {item.name}
                      <span className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-400 to-secondary-400 transition-all duration-300 group-hover:w-full`}></span>
                    </span>
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link
              href="/quote"
              className="group relative overflow-hidden bg-gradient-to-r from-accent-500 to-secondary-500 hover:from-accent-600 hover:to-secondary-600 text-white font-bold px-8 py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>Get Quote</span>
                <svg className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              type="button"
              className={`p-2 rounded-md transition-colors duration-200 ${
                scrolled
                  ? 'text-gray-700 hover:text-secondary-600'
                  : 'text-white hover:text-secondary-200'
              }`}
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white rounded-lg shadow-medium mt-2">
              {navigation.map((item) => (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={`block px-3 py-2 text-base font-medium rounded-md transition-colors duration-200 ${
                      isActive(item.href)
                        ? 'text-secondary-600 bg-secondary-50'
                        : 'text-gray-700 hover:text-secondary-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                  {item.children && (
                    <div className="ml-4 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-3 py-2 text-sm text-gray-600 hover:text-secondary-600 hover:bg-gray-50 rounded-md transition-colors duration-200"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              
              {/* Mobile CTA */}
              <div className="pt-4 border-t border-gray-200">
                <Link
                  href="/quote"
                  className="block w-full btn-secondary text-center text-black font-semibold"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Get Quote
                </Link>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
